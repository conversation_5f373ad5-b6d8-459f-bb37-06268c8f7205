# PowerShell脚本：自动打开Windows软键盘
# 作者：AI助手
# 日期：2025-08-04

Write-Host "正在打开软键盘..." -ForegroundColor Green

try {
    # 方法1：直接启动osk
    Start-Process "osk" -ErrorAction Stop
    Write-Host "软键盘已成功启动！" -ForegroundColor Green
}
catch {
    Write-Host "尝试备用方法..." -ForegroundColor Yellow
    try {
        # 方法2：通过完整路径启动
        Start-Process "$env:SystemRoot\System32\osk.exe" -ErrorAction Stop
        Write-Host "软键盘已成功启动！" -ForegroundColor Green
    }
    catch {
        Write-Host "无法启动软键盘，请检查系统设置。" -ForegroundColor Red
        Write-Host "错误信息：$($_.Exception.Message)" -ForegroundColor Red
    }
}

# 可选：等待用户按键
Write-Host "按任意键退出..." -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
