# 软键盘快捷启动工具

这个工具包含多种方式来快速打开Windows系统的软键盘（屏幕键盘）。

## 文件说明

### 1. 打开软键盘.bat
- **类型**：Windows批处理文件
- **特点**：显示命令行窗口，有执行反馈
- **使用方法**：双击运行
- **适用场景**：需要看到执行过程和结果

### 2. 打开软键盘.ps1
- **类型**：PowerShell脚本
- **特点**：彩色输出，错误处理完善
- **使用方法**：右键选择"使用PowerShell运行"或在PowerShell中执行
- **适用场景**：需要详细的执行信息和错误处理

### 3. 软键盘快捷方式.vbs
- **类型**：VBScript脚本
- **特点**：静默运行，无窗口弹出
- **使用方法**：双击运行
- **适用场景**：需要快速、安静地启动软键盘

## 使用说明

1. **最简单的方式**：双击 `软键盘快捷方式.vbs` 文件
2. **需要反馈信息**：双击 `打开软键盘.bat` 文件
3. **高级用户**：使用 `打开软键盘.ps1` PowerShell脚本

## 创建桌面快捷方式

1. 右键点击任意脚本文件
2. 选择"发送到" → "桌面快捷方式"
3. 重命名快捷方式为"软键盘"

## 设置快捷键

1. 右键点击桌面快捷方式
2. 选择"属性"
3. 在"快捷键"栏设置组合键（如 Ctrl+Alt+K）
4. 点击"确定"

## 故障排除

如果软键盘无法启动：
1. 检查Windows辅助功能是否启用
2. 确保用户有足够的权限
3. 尝试以管理员身份运行脚本

## 系统要求

- Windows 7 及以上版本
- 已启用Windows辅助功能
